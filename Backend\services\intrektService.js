import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config();

class IntrektService {
  constructor() {
    this.baseURL = process.env.INTREKT_BASE_URL || 'https://api.interakt.ai';
    this.apiKey = process.env.INTREKT_API_KEY;
    this.secretKey = process.env.INTREKT_SECRET_KEY;

    if (!this.apiKey) {
      console.warn('⚠️ INTREKT_API_KEY not found in environment variables');
    }
    if (!this.secretKey) {
      console.warn('⚠️ INTREKT_SECRET_KEY not found in environment variables');
    }

    console.log('🔧 Intrekt Service initialized with secret key:', this.secretKey ? 'Present' : 'Missing');
  }

  // Generate random OTP
  generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // Format phone number for WhatsApp (remove +91 if present)
  formatPhoneNumber(phoneNumber) {
    let formatted = phoneNumber.toString().replace(/\D/g, '');
    
    // Remove country code if present
    if (formatted.startsWith('91') && formatted.length === 12) {
      formatted = formatted.substring(2);
    }
    
    return formatted;
  }

  // Send OTP via WhatsApp using Intrekt
  async sendOTP(phoneNumber, otp = null) {
    try {
      if (!this.apiKey) {
        throw new Error('Intrekt API key not configured');
      }

      const generatedOTP = otp || this.generateOTP();
      const formattedPhone = this.formatPhoneNumber(phoneNumber);

      console.log('📱 Intrekt Service: Sending OTP to:', formattedPhone);
      console.log('🔢 Intrekt Service: Generated OTP:', generatedOTP);

      // Try simple text message first, then template
      const messageData = {
        countryCode: "+91",
        phoneNumber: formattedPhone,
        type: "Text",
        data: {
          message: `Your FASTAGCAB verification code is ${generatedOTP}. Valid for 15 minutes. Do not share this code.`
        }
      };

      console.log('🌐 Intrekt Service: Making API request to:', `${this.baseURL}/v1/public/message/`);
      console.log('📤 Intrekt Service: Request data:', JSON.stringify(messageData, null, 2));

      const headers = {
        'Authorization': `Basic ${this.apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };

      // Add secret key if available (check different header formats)
      if (this.secretKey) {
        headers['X-Secret-Key'] = this.secretKey;
        headers['Secret-Key'] = this.secretKey;
        headers['X-API-Secret'] = this.secretKey;
      }

      console.log('🔑 Request headers:', JSON.stringify(headers, null, 2));

      const response = await fetch(`${this.baseURL}/v1/public/message/`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(messageData)
      });

      const responseData = await response.json();
      console.log('📥 Intrekt Service: API Response:', JSON.stringify(responseData, null, 2));

      if (response.ok) {
        console.log('✅ Intrekt Service: OTP sent successfully');
        return {
          success: true,
          message: 'OTP sent successfully via WhatsApp',
          otp: generatedOTP, // Return OTP for storage
          data: responseData
        };
      } else {
        console.error('❌ Intrekt Service: API Error:', responseData);
        return {
          success: false,
          message: responseData.message || 'Failed to send OTP via WhatsApp',
          error: responseData
        };
      }
    } catch (error) {
      console.error('🚨 Intrekt Service: Send OTP error:', error);
      return {
        success: false,
        message: 'Error sending OTP via WhatsApp',
        error: error.message
      };
    }
  }

  // Send custom WhatsApp message using Intrekt
  async sendMessage(phoneNumber, templateName, bodyValues = [], headerValues = [], buttonValues = {}) {
    try {
      if (!this.apiKey) {
        throw new Error('Intrekt API key not configured');
      }

      const formattedPhone = this.formatPhoneNumber(phoneNumber);

      const messageData = {
        countryCode: "+91",
        phoneNumber: formattedPhone,
        type: "Template",
        template: {
          name: templateName,
          languageCode: "en",
          headerValues: headerValues,
          bodyValues: bodyValues,
          buttonValues: buttonValues
        }
      };

      console.log('🌐 Intrekt Service: Sending custom message');
      console.log('📤 Intrekt Service: Message data:', JSON.stringify(messageData, null, 2));

      const response = await fetch(`${this.baseURL}/v1/public/message/`, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(messageData)
      });

      const responseData = await response.json();

      if (response.ok) {
        console.log('✅ Intrekt Service: Message sent successfully');
        return {
          success: true,
          message: 'Message sent successfully via WhatsApp',
          data: responseData
        };
      } else {
        console.error('❌ Intrekt Service: Message send error:', responseData);
        return {
          success: false,
          message: responseData.message || 'Failed to send message via WhatsApp',
          error: responseData
        };
      }
    } catch (error) {
      console.error('🚨 Intrekt Service: Send message error:', error);
      return {
        success: false,
        message: 'Error sending message via WhatsApp',
        error: error.message
      };
    }
  }

  // Test API connection
  async testConnection() {
    try {
      if (!this.apiKey) {
        return {
          success: false,
          message: 'Intrekt API key not configured'
        };
      }

      console.log('🔍 Intrekt Service: Testing API connection...');
      
      // Test with a simple API call (you might need to adjust this based on Intrekt's API)
      const response = await fetch(`${this.baseURL}/v1/public/message/`, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${this.apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          countryCode: "+91",
          phoneNumber: "0000000000", // Test number
          type: "Template",
          template: {
            name: "test_template",
            languageCode: "en",
            headerValues: [],
            bodyValues: [],
            buttonValues: {}
          }
        })
      });

      const responseData = await response.json();
      
      return {
        success: response.ok,
        message: response.ok ? 'Intrekt API connection successful' : 'Intrekt API connection failed',
        data: responseData
      };
    } catch (error) {
      console.error('🚨 Intrekt Service: Connection test error:', error);
      return {
        success: false,
        message: 'Error testing Intrekt API connection',
        error: error.message
      };
    }
  }
}

export default new IntrektService();
