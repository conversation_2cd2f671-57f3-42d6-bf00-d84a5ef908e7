<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Test OTP Integration</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 600px;
        margin: 40px auto;
        padding: 20px;
      }
      textarea,
      input {
        width: 100%;
        padding: 8px;
        margin-top: 8px;
        box-sizing: border-box;
      }
      button {
        padding: 8px 16px;
        margin-top: 12px;
        background: #0066cc;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      button:disabled {
        background: #cccccc;
        cursor: not-allowed;
      }
      #status {
        white-space: pre-wrap;
        background: #f3f3f3;
        padding: 12px;
        border-radius: 4px;
        margin-top: 12px;
      }
      .hidden {
        display: none;
      }
      .success {
        color: green;
      }
      .error {
        color: red;
      }
    </style>
  </head>
  <body>
    <h2>🧪 Test OTP Integration</h2>
    <p>This test uses the same configuration as your React Native app</p>

    <label>Recipient Phone (without +):</label>
    <input id="phone" placeholder="919999999999" value="918959305284" />

    <button id="sendBtn">Send OTP</button>

    <div id="verifyBlock" class="hidden">
      <label>Enter OTP:</label>
      <input id="otpInput" maxlength="6" />
      <button id="verifyBtn">Verify OTP</button>
    </div>

    <h3>Status / Response</h3>
    <div id="status"></div>

    <script>
      // ====== CONFIG – MATCHING YOUR REACT NATIVE APP ======
      const API_KEY = "NThLVHR6SGgwNW1pUXZlZmpWTS05N2c3d2czeUl4X3owX0dtZTlxVmNfMDo=";
      const TEMPLATE = "otp_verification";
      const LANGUAGE = "en";
      const COUNTRY_CD = "+91";
      // =====================================================

      let generatedOTP = "";
      let phoneNumber = "";

      const $ = (id) => document.getElementById(id);
      const log = (msg, type = 'info') => {
        const timestamp = new Date().toLocaleTimeString();
        const statusEl = $("status");
        const className = type === 'success' ? 'success' : type === 'error' ? 'error' : '';
        statusEl.innerHTML += `<span class="${className}">[${timestamp}] ${msg}</span>\n`;
        statusEl.scrollTop = statusEl.scrollHeight;
      };

      $("sendBtn").onclick = async () => {
        $("status").innerHTML = "";
        phoneNumber = $("phone").value.trim();
        
        if (!phoneNumber) {
          log("Please enter a phone number", 'error');
          return;
        }

        generatedOTP = Math.floor(100000 + Math.random() * 900000).toString();
        log(`Generated OTP: ${generatedOTP}`, 'success');

        const payload = {
          countryCode: COUNTRY_CD,
          phoneNumber: phoneNumber,
          callbackData: "otp-verification",
          type: "Template",
          template: {
            name: TEMPLATE,
            languageCode: LANGUAGE,
            bodyValues: [generatedOTP],
            buttonValues: { 0: [generatedOTP] }
          },
        };

        log("Sending OTP request...");
        log(`Payload: ${JSON.stringify(payload, null, 2)}`);

        try {
          $("sendBtn").disabled = true;
          $("sendBtn").textContent = "Sending...";

          const res = await fetch("https://api.interakt.ai/v1/public/message/", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: "Basic " + API_KEY,
            },
            body: JSON.stringify(payload),
          });

          const data = await res.json();
          log(`HTTP ${res.status} ${res.statusText}`);
          log(`Response: ${JSON.stringify(data, null, 2)}`);

          if (res.ok && data.result) {
            log("✅ OTP sent successfully!", 'success');
            log("Check WhatsApp for the message", 'success');
            $("verifyBlock").classList.remove("hidden");
            
            // Store OTP in localStorage for verification (simulating AsyncStorage)
            localStorage.setItem(`test_otp_${phoneNumber}`, JSON.stringify({
              otp: generatedOTP,
              expiresAt: Date.now() + (15 * 60 * 1000) // 15 minutes
            }));
          } else {
            log("❌ Failed to send OTP", 'error');
            log(`Error: ${data.message || 'Unknown error'}`, 'error');
          }
        } catch (err) {
          log(`❌ Network Error: ${err.message}`, 'error');
        } finally {
          $("sendBtn").disabled = false;
          $("sendBtn").textContent = "Send OTP";
        }
      };

      $("verifyBtn").onclick = () => {
        const userOtp = $("otpInput").value.trim();
        
        if (!userOtp) {
          log("Please enter the OTP", 'error');
          return;
        }

        // Simulate the verification logic from your React Native app
        const storedData = localStorage.getItem(`test_otp_${phoneNumber}`);
        
        if (!storedData) {
          log("❌ No OTP found. Please request a new OTP.", 'error');
          return;
        }

        const { otp, expiresAt } = JSON.parse(storedData);
        
        if (Date.now() > expiresAt) {
          localStorage.removeItem(`test_otp_${phoneNumber}`);
          log("❌ OTP has expired. Please request a new OTP.", 'error');
          return;
        }

        if (otp === userOtp) {
          localStorage.removeItem(`test_otp_${phoneNumber}`);
          log("✅ OTP verified successfully!", 'success');
          log("🎉 Integration test passed!", 'success');
        } else {
          log("❌ Invalid OTP. Please try again.", 'error');
          log(`Expected: ${otp}, Got: ${userOtp}`, 'error');
        }
      };

      // Auto-focus phone input
      $("phone").focus();
    </script>
  </body>
</html>
